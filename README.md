# 遥感图像分类系统

基于深度学习的遥感图像场景分类系统，支持 21 个场景类别的自动识别，提供 Web 界面和 API 接口。

## 功能特性

- 支持多种深度学习模型（ResNet50、DenseNet201、ViT、Swin Transformer）
- 模型优化技术（剪枝、知识蒸馏、量化）
- 现代化 Web 界面，支持拖拽上传
- RESTful API 接口
- 自适应微调功能

## Docker 部署

### 构建镜像

```bash
docker build -t rsic:latest .
```

### 运行容器

```bash
docker run -d \
  --name rsic-app \
  -p 8000:8000 \
  --gpus all \
  -v $(pwd)/old_dataset:/app/old_dataset \
  -v $(pwd)/outputs:/app/outputs \
  rsic:latest
```

### 访问地址

- **Web 界面**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/health

## 数据集

支持 21 个遥感场景类别：
airplane, baseball_field, basketball_court, beach, bridge, chaparral, dense_residential, forest, freeway, golf_course, harbor, intersection, mobile_home_park, overpass, parking_lot, railway, river, runway, sparse_residential, storage_tank, tennis_court

数据集放置在 `old_dataset/` 目录下，按类别组织。

## 使用方法

### Web 界面

1. 访问 http://localhost:8000
2. 上传遥感图像
3. 选择预测模型
4. 查看分类结果

### API 调用

```python
import requests

# 图像预测
with open('image.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/api/predict',
        files={'image': f},
        data={'model_key': 'resnet50'}
    )
    result = response.json()
    print(result['predictions'])
```

## 模型训练

### 自动化训练

```bash
# 训练单个模型
python src/main.py --model_names resnet50 --epochs 20

# 训练多个模型并优化
python src/main.py \
    --model_names resnet50 densenet201 \
    --pretrained \
    --epochs 20 \
    --optimize_mode all
```

### 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 启动Web应用
python src/web_app.py
```

## 故障排除

### 模型和数据集检测问题 ✅ 已修复

**问题描述**: Web 应用启动时显示"无法检测到模型和数据集"，健康检查显示模型数量为 0

**修复内容**:

1. **修复全局变量引用问题**: 修改 `web_app.py` 中的模块导入方式

   ```python
   # 修改前：直接导入全局变量（在模块加载时为空）
   from app import MODEL_PATHS, CLASSES

   # 修改后：导入模块并动态访问全局变量
   import app as app_module
   # 使用 app_module.MODEL_PATHS 和 app_module.CLASSES
   ```

2. **确保模型发现功能正常工作**: 验证 `discover_available_models()` 函数能正确检测到：

   - 4 种模型类型：densenet201, resnet50, swin_t, vit_s_16
   - 3 种模型变体：原始、剪枝、蒸馏（共 12 个模型）
   - 21 个图像分类类别

3. **验证数据集加载功能**: 确认能正确加载 `old_dataset` 和 `new_dataset` 中的样本图片

**验证方法**: 运行诊断和测试脚本

```bash
# 诊断项目结构和文件
python diagnose_paths.py

# 测试模型加载功能
python test_model_loading.py

# 测试服务器连接
python test_server.py
```

**修复结果**:

- ✅ 成功检测到 12 个可用模型
- ✅ 成功加载 21 个图像类别
- ✅ 数据集样本加载正常
- ✅ 所有 API 端点工作正常

### 前端资源加载失败 ✅ 已修复

**问题描述**: 前端页面显示失败，出现 CSS、JS 文件 404 错误

**修复内容**:

1. **修复 HTML 资源路径**: 将相对路径改为绝对路径

   - `css/style.css` → `/static/css/style.css`
   - `js/main.js` → `/static/js/main.js`

2. **修复静态文件挂载路径**: 更新 web_app.py 中的静态目录路径

   ```python
   static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")
   app.mount("/static", StaticFiles(directory=static_dir), name="static")
   ```

3. **添加 favicon**: 使用 SVG emoji 避免 favicon.ico 404 错误

**验证方法**: 运行测试脚本

```bash
python test_frontend.py
```

### 文件结构确认

确认以下文件结构正确：

```
项目根目录/
├── src/
│   └── web_app.py
├── static/
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── main.js
│   └── index.html
└── test_frontend.py
```

### 常见问题

- **404 错误**: 检查静态文件路径和文件是否存在
- **CORS 错误**: 确认 CORS 中间件已正确配置
- **模型加载失败**: 确认模型文件在 outputs 目录中
- **端口占用**: 如果 8000 端口被占用，修改 web_app.py 中的端口号
